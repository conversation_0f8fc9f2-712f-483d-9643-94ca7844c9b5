--cpu=Cortex-M4.fp.sp
"test01\startup_stm32f429xx.o"
"test01\main.o"
"test01\gpio.o"
"test01\adc.o"
"test01\dac.o"
"test01\dma.o"
"test01\tim.o"
"test01\usart.o"
"test01\stm32f4xx_it.o"
"test01\stm32f4xx_hal_msp.o"
"test01\stm32f4xx_hal_adc.o"
"test01\stm32f4xx_hal_adc_ex.o"
"test01\stm32f4xx_ll_adc.o"
"test01\stm32f4xx_hal_rcc.o"
"test01\stm32f4xx_hal_rcc_ex.o"
"test01\stm32f4xx_hal_flash.o"
"test01\stm32f4xx_hal_flash_ex.o"
"test01\stm32f4xx_hal_flash_ramfunc.o"
"test01\stm32f4xx_hal_gpio.o"
"test01\stm32f4xx_hal_dma_ex.o"
"test01\stm32f4xx_hal_dma.o"
"test01\stm32f4xx_hal_pwr.o"
"test01\stm32f4xx_hal_pwr_ex.o"
"test01\stm32f4xx_hal_cortex.o"
"test01\stm32f4xx_hal.o"
"test01\stm32f4xx_hal_exti.o"
"test01\stm32f4xx_hal_dac.o"
"test01\stm32f4xx_hal_dac_ex.o"
"test01\stm32f4xx_hal_tim.o"
"test01\stm32f4xx_hal_tim_ex.o"
"test01\stm32f4xx_hal_uart.o"
"test01\system_stm32f4xx.o"
"test01\ebtn.o"
"test01\ringbuffer.o"
"test01\scheduler.o"
"test01\led_app.o"
"test01\key_app.o"
"test01\btn_app.o"
"test01\usart_app.o"
"test01\adc_app.o"
"test01\dac_app.o"
"test01\basicmathfunctions.o"
"test01\basicmathfunctionsf16.o"
"test01\bayesfunctions.o"
"test01\bayesfunctionsf16.o"
"test01\commontables.o"
"test01\commontablesf16.o"
"test01\complexmathfunctions.o"
"test01\complexmathfunctionsf16.o"
"test01\controllerfunctions.o"
"test01\distancefunctions.o"
"test01\distancefunctionsf16.o"
"test01\fastmathfunctions.o"
"test01\fastmathfunctionsf16.o"
"test01\filteringfunctions.o"
"test01\filteringfunctionsf16.o"
"test01\interpolationfunctions.o"
"test01\interpolationfunctionsf16.o"
"test01\matrixfunctions.o"
"test01\matrixfunctionsf16.o"
"test01\quaternionmathfunctions.o"
"test01\svmfunctions.o"
"test01\svmfunctionsf16.o"
"test01\statisticsfunctions.o"
"test01\statisticsfunctionsf16.o"
"test01\supportfunctions.o"
"test01\supportfunctionsf16.o"
"test01\transformfunctions.o"
"test01\transformfunctionsf16.o"
"test01\windowfunctions.o"
--strict --scatter "test01\test01.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "..\..\test01.map" -o test01\test01.axf