test01/interpolationfunctions.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\InterpolationFunctions.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_bilinear_interp_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_bilinear_interp_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_bilinear_interp_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_bilinear_interp_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_linear_interp_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_linear_interp_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_linear_interp_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_linear_interp_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_spline_interp_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_spline_interp_init_f32.c
