test01/basicmathfunctions.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\BasicMathFunctions.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_abs_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_abs_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_abs_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_abs_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_abs_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_add_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_add_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_add_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_add_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_add_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_and_u16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_and_u32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_and_u8.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_dot_prod_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_dot_prod_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_dot_prod_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_dot_prod_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_dot_prod_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_mult_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_mult_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_mult_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_mult_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_mult_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_negate_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_negate_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_negate_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_negate_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_negate_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_not_u16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_not_u32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_not_u8.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_offset_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_offset_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_offset_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_offset_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_offset_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_or_u16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_or_u32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_or_u8.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_scale_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_scale_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_scale_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_scale_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_scale_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_shift_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_shift_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_shift_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_sub_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_sub_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_sub_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_sub_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_sub_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_xor_u16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_xor_u32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_xor_u8.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_clip_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_clip_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_clip_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_clip_q7.c
