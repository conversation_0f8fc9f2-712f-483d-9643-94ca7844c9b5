#ifndef ADC_APP_H
#define ADC_APP_H

#include "mydefine.h"
void adc_tim_dma_init(void);
void adc_task(void);
void My_FFT_Init(void);
void fir_init(void);
void update_hmi_display(void);

// 信号分析函数声明
typedef struct {
    float am_freq;
    float am;
} AM_Params;

typedef struct {
    float fm_freq;
    float delta_f;
    float mf_value;
} FM_Params;

typedef struct {
    float ask_freq;
} ASK_Params;

typedef struct {
    float fsk_freq;
    float h_value;
} FSK_Params;

typedef struct {
    float psk_freq;
} PSK_Params;

AM_Params Analysis_AM(void);
FM_Params Analysis_FM(void);
ASK_Params Analysis_ASK(void);
FSK_Params Analysis_FSK(void);
PSK_Params Analysis_PSK(void);
#endif
