test01/supportfunctions.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\SupportFunctions.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_barycenter_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_bitonic_sort_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\PrivateInclude\arm_sorting.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\controller_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\quaternion_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\window_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_bubble_sort_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_copy_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_copy_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_copy_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_copy_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_copy_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_fill_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_fill_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_fill_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_fill_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_fill_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_heap_sort_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_insertion_sort_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_merge_sort_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_merge_sort_init_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_quick_sort_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_selection_sort_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_sort_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_sort_init_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_weighted_average_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f64_to_float.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f64_to_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f64_to_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f64_to_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_float_to_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_float_to_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_float_to_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_float_to_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q15_to_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q15_to_float.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q15_to_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q15_to_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q31_to_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q31_to_float.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q31_to_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q31_to_q7.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q7_to_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q7_to_float.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q7_to_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q7_to_q31.c
