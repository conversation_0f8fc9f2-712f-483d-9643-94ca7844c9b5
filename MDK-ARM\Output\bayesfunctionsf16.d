./output/bayesfunctionsf16.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BayesFunctions\BayesFunctionsF16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BayesFunctions\arm_gaussian_naive_bayes_predict_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h
