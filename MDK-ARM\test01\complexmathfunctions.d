test01/complexmathfunctions.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\ComplexMathFunctions.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_conj_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_conj_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_conj_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_dot_prod_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_dot_prod_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_dot_prod_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_fast_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_squared_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_squared_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_squared_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_squared_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_cmplx_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_cmplx_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_cmplx_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_cmplx_q31.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_real_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_real_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_real_q31.c
