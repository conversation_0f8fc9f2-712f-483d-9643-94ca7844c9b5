#include "adc_app.h"
#define BUFFER_SIZE 4096
float sampling_frequency =250000.0f;
extern DMA_HandleTypeDef hdma_adc1;

uint32_t dac_val_buffer[BUFFER_SIZE];
//uint32_t res_val_buffer[BUFFER_SIZE / 2];
__IO uint32_t adc_val_buffer[BUFFER_SIZE];
__IO float voltage;
__IO uint8_t AdcConvEnd = 0;
uint8_t wave_analysis_flag = 1; // ���η�����־λ
uint8_t wave_query_type = 0;    // ���β�ѯ���ͣ�0=ȫ��, 1=����, 2=Ƶ��, 3=���ֵ

void adc_tim_dma_init(void)
{ 
    HAL_TIM_Base_Start(&htim3);
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // ���ð봫���ж�
}

// ADC ת����ɻص�
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef *hadc)
{
    UNUSED(hadc);
    if (hadc == &hadc1) // ȷ���� hadc1 ���
    {
        HAL_ADC_Stop_DMA(hadc); // ֹͣ DMA���ȴ�����
        AdcConvEnd = 1;         // ���ñ�־λ
    }
}

WaveformInfo wave_data;

#define FFT_LENGTH 4096 // �����adc����������Ҫ����һ��
arm_cfft_radix4_instance_f32 scfft;
float FFT_InputBuf[FFT_LENGTH * 2];
float FFT_OutputBuf[FFT_LENGTH];


void My_FFT_Init(void)
{
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1); // ��ʼ��scfft�ṹ��,�趨FFT����
}

void concentrate_energy(float *fft_data, uint16_t fft_length, uint8_t concentration_factor)
{
    static float temp_buffer[4096]; // ��ʱ������
    
    // ����ԭʼ����
    for (int i = 0; i < fft_length; i++) {
        temp_buffer[i] = fft_data[i];
    }
    
    // ��ÿ��Ƶ����������ۼ������˱߽磩
    for (int i = concentration_factor/2; i < fft_length - concentration_factor/2; i++) {
        // ����Ƿ��Ǿֲ����ֵ
        int is_local_max = 1; // ʹ��int���bool
        float max_value = temp_buffer[i];
        
        for (int j = i - concentration_factor/2; j <= i + concentration_factor/2; j++) {
            if (j != i && temp_buffer[j] > max_value) {
                is_local_max = 0; // ʹ��0���false
                break;
            }
        }
        
        // ����Ǿֲ����ֵ���ۼ���Χ����
        if (is_local_max) {
            float total_energy = 0.0f;
            for (int j = i - concentration_factor/2; j <= i + concentration_factor/2; j++) {
                total_energy += temp_buffer[j];
                if (j != i) {
                    fft_data[j] = 0.0f; // �����Χ�������
                }
            }
            fft_data[i] = total_energy; // ���������ۼ������ĵ�
        }
    }
}
void calculate_fft(
    const arm_cfft_radix4_instance_f32* scfft,
    volatile const uint32_t* adc_val_buffer,
    float32_t* fft_output_buf,
    uint32_t fft_length)
{
    // --- Static buffers to avoid stack overflow with large FFT_LENGTH ---
    // These buffers are allocated only once.
    static float32_t signal_float[FFT_LENGTH];
    static float32_t hanning_window[FFT_LENGTH];
    static float32_t signal_windowed[FFT_LENGTH];
    // FFT input buffer requires 2*FFT_LENGTH because it holds complex numbers (real, imag)
    static float32_t fft_input_buf[FFT_LENGTH * 2];
    
    // 1. Convert raw ADC integer values to normalized float voltage values
    // This part normalizes 12-bit ADC values (0-4095) to a 0-3.3V range.
    for (int i = 0; i < fft_length; i++)
    {
        signal_float[i] = (float32_t)adc_val_buffer[i] / 4096.0f * 3.3f;
    }

    // 2. Generate a Hanning window
    // A window function is applied to reduce spectral leakage.
    arm_hanning_f32(hanning_window, fft_length);

    // 3. Apply the window to the signal (element-wise multiplication)
    arm_mult_f32(signal_float, hanning_window, signal_windowed, fft_length);

    // 4. Prepare the input buffer for the CFFT function
    // The FFT function requires a complex input (real, imaginary, real, imaginary, ...).
    // We set the real part to our windowed signal and the imaginary part to zero.
    for (int i = 0; i < fft_length; i++)
    {
        fft_input_buf[2 * i]     = signal_windowed[i]; // Real part
       
        fft_input_buf[2 * i + 1] = 0.0f;                // Imaginary part
    }

    // 5. Perform the CFFT (Complex Fast Fourier Transform)
    // The calculation is done in-place, meaning fft_input_buf is overwritten with the result.
    arm_cfft_radix4_f32(scfft, fft_input_buf);

    // 6. Calculate the magnitude of the complex FFT output
    // The output is now |C|, where C = R + jI. The magnitude is sqrt(R^2 + I^2).
    // The result is stored in the user-provided output buffer.
    arm_cmplx_mag_f32(fft_input_buf, fft_output_buf, fft_length);
}
void Analysis_AM(void)
{
    // ���һ�Ƶ������ֱ��������
    float max_harmonic = 0;   // ���г������
    int max_harmonic_idx = 0; // ���г����������

    for (int i = 10; i < FFT_LENGTH / 2; i++) // ֻ�迼��ǰһ��Ƶ�ף�NyquistƵ�ʣ�
    {
        if (FFT_OutputBuf[i] > max_harmonic)
        {
            max_harmonic = FFT_OutputBuf[i];
            max_harmonic_idx = i;
        }
    }

    // ����FFTƵ�ʣ�Hz��
    // Ƶ�ʼ��㹫ʽ: freq = idx * sampling_freq / FFT_LENGTH
    float fft_frequency = (float)max_harmonic_idx * sampling_frequency / (float)FFT_LENGTH;
   // my_printf(&huart1,"max_harmonic_idx=%d\r\nmax_freq=%.2f\r\n%.2f\r\n",max_harmonic_idx,fft_frequency,FFT_OutputBuf[max_harmonic_idx]);
    
    float right_freq,right_amp,left_freq, left_amp;
    for(int i=max_harmonic_idx+1;i<BUFFER_SIZE/2;i++)
    {
        
        if(FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1]&&FFT_OutputBuf[i]>10)//���10��Ҫ�Լ����ƣ���Ȼ���ܼ�⵽һЩ0.����
        {
             right_freq = i * sampling_frequency / (float)FFT_LENGTH;
             right_amp=FFT_OutputBuf[i];
            //my_printf(&huart1,"right_harmonic_idx=%d\r\nright_freq=%.2\r\nf%.2f\r\n",i,right_freq,FFT_OutputBuf[i]);
            break;
        }
    }
    for(int i=max_harmonic_idx-1;i>100;i--)
    {
        
        if(FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1]&&FFT_OutputBuf[i]>10)//���10��Ҫ�Լ����ƣ���Ȼ���ܼ�⵽һЩ0.����
        {
             left_freq = i * sampling_frequency / (float)FFT_LENGTH;
             left_amp=FFT_OutputBuf[i];
            //my_printf(&huart1,"left_harmonic_idx=%d\r\nleft_freq=%.2f\r\n%.2f\r\n",i,left_freq,FFT_OutputBuf[i]);
            break;
        }
    }
    float am_freq=(right_freq-left_freq)/2;
    float am=(left_amp+right_amp)/ max_harmonic;
    my_printf(&huart1,"AM:\r\n");
    my_printf(&huart1,"am_freq=%.2f,am=%.2f\r\n",am_freq,am);
    
    my_printf(&huart3, "page AM\xff\xff\xff");
    HMI_Send_Float(huart3,"x0",am_freq,2);
    HMI_Send_Float(huart3,"x1",am,2);
    /*
     // ����2: ���߽�������ȷ����ֵ�㲻����Ƶ�׵ı�Ե�����԰�ȫ��ȡ�������ھ�
    if (max_harmonic_idx > 0 && max_harmonic_idx < (FFT_LENGTH / 2 - 1))
    {
        // ����3: ��ȡ��ֵ������������ߵķ���ֵ
        float M_left = FFT_OutputBuf[max_harmonic_idx - 1];
        float M_peak = max_harmonic; // ���� FFT_OutputBuf[max_harmonic_idx]
        float M_right = FFT_OutputBuf[max_harmonic_idx + 1];

        // ����4: ��������ֵ delta
        float numerator = M_left - M_right;
        float denominator = M_left - 2.0f * M_peak + M_right;
        
        // ��ȫ��飺��������㡣�����ĸΪ�㣬˵�������㹲�߻��ֵ�������޷���ֵ��ֱ��ʹ�ô���ֵ
        if (fabs(denominator) > 1e-3) // ʹ��һ����С�������ж��Ƿ�ӽ�����
        {
            float delta = 0.5f * numerator / denominator;

            // ����5: ���㾫ȷƵ��
            // ��ȷ���� = �������� + ����ֵ
            // ��ȷƵ�� = (��ȷ���� * ������) / FFT����
            precise_frequency = ((float)max_harmonic_idx + delta) * sampling_frequency / (float)FFT_LENGTH;
        }
    }*/
    // ��FFTƵ��ӳ�������Ƶ��
    //return Map_FFT_To_Input_Frequency(fft_frequency);
}


typedef struct {
    int idx;
    float harmonic;
    float freq;
}FM_Spectrum;
FM_Spectrum fm_spectrum[20];

typedef struct {
    float mf;  // ����ָ��
    float J0;  // ������ J0 ��ֵ
    float J1;  // ������ J1 ��ֵ
    float J2;  // ������ J2 ��ֵ
} BesselLutEntry;
const BesselLutEntry bessel_lut[] = {
    {1.0, 1.739, 3.826},
    {1.1,1.529,3.364},
    {1.2, 1.347, 3.132},
    {1.3, 1.190, 2.797},
    {1.4, 1.046, 2.618},
    {1.5, 0.920, 2.347},
    {1.6, 0.798, 2.218},
    {1.7, 0.691, 2.011},
    {1.8, 0.584, 1.902},
    {1.9, 0.487, 1.722},
    {2.0, 0.388, 1.635},
    {2.1, 0.296, 1.483},
    {2.2, 0.198, 1.408},
    {2.3, 0.107, 1.276},
    {2.4, 0.006, 1.206},
    {2.5, 0.102, 1.092},
    {2.6, 0.206, 1.026},
    {2.7, 0.321, 0.923},
    {2.8, 0.451, 0.858},
    {2.9, 0.600, 0.763},
    {3.0, 0.767, 0.698},
    {3.1, 0.970, 0.608},
    {3.2, 1.226, 0.539},
    {3.3, 1.562, 0.454},
    {3.4, 2.034, 0.381},
    {3.5, 2.767, 0.296},
    {3.6, 4.126, 0.213},
    {3.7, 7.182, 0.128},
    {3.8, 31.000, 0.032},
    {3.9, 13.447, 0.076},
    {4.0, 6.015, 0.181},
    {4.1, 3.713, 0.302},
    {4.2, 2.712, 0.447},
    {4.3, 2.100, 0.601},
    {4.4, 1.685, 0.812},
    {4.5, 1.397, 1.037},
    {4.6, 1.152, 1.389},
    {4.7 ,0.966, 1.805},
    {4.8, 0.805, 2.569},
    {4.9, 0.671, 3.755},
    {5.0, 0.543, 6.979}
    // ... �����Ҫ������չ��������
};
const int LUT_SIZE = sizeof(bessel_lut) / sizeof(BesselLutEntry);
float find_mf_from_harmonics(float j0_measured, float j1_measured, float j2_measured) 
{
    float min_error = -1.0; // ��ʼ��Ϊ-1��һ���ǳ������
    float best_mf = 0.0;
    
    float J0_diff=0.0f,J1_diff=0.0f;
    J0_diff=j0_measured/j1_measured;
    J1_diff=j1_measured/j2_measured;
    my_printf(&huart1,"%.3f, %.3f\r\n",J0_diff,J1_diff);
    for (int i = 0; i < LUT_SIZE; i++) {
        // �������ֵ����е�ǰ�����ݵ������ƽ���ͣ�
        float err_j0 = J0_diff - bessel_lut[i].J0;
        float err_j1 = J1_diff - bessel_lut[i].J1;     
        float current_error = err_j0*err_j0 + err_j1*err_j1 ;
        //my_printf(&huart1,"%d=error=%.3f\r\n",i,current_error);
        // ����ǵ�һ�λ����ҵ��˸�С��������½��
        if (min_error < 0 || current_error < min_error) {
            min_error = current_error;
            best_mf = bessel_lut[i].mf;
        }
    }
    
    return best_mf;
}
void Analysis_FM(void)
{
    uint8_t index=0;
    for (int i = 10; i < 825; i++) // �ز�Ϊ819,1kʱ�ұ�Ϊ836
    {
        if (FFT_OutputBuf[i] > 5.0f&&FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1])
        {
            fm_spectrum[index].idx=i;
            fm_spectrum[index].harmonic=FFT_OutputBuf[i];
            fm_spectrum[index].freq=(float)fm_spectrum[index].idx* sampling_frequency / (float)FFT_LENGTH;
            index++;
        }
    }
    for(int i=0;i<index;i++)
    {
        my_printf(&huart1,"idx=%d amp=%.3f freq=%.3f\r\n",fm_spectrum[i].idx,fm_spectrum[i].harmonic,fm_spectrum[i].freq);
    }
    float freq_diff[20];
    uint8_t freq_diff_index=0;
    float fm_freq;
    
    fm_freq=(fm_spectrum[index-1].freq-fm_spectrum[0].freq)/(float)(index-1);
    
    float measured_J0 = FFT_OutputBuf[819];
    float measured_J1 = fm_spectrum[index-2].harmonic; // ��ȷ��index-2��ӦJ1
    float measured_J2 = fm_spectrum[index-3].harmonic; // ��ȷ��index-3��ӦJ2
    float mf_value = find_mf_from_harmonics(measured_J0, measured_J1, measured_J2);
    my_printf(&huart1,"FM:\r\n");
    my_printf(&huart1,"fm_freq=%.3f\r\nmf=%.1f\r\ndelta_f=%.2f\r\n",fm_freq,mf_value,fm_freq*mf_value);
    my_printf(&huart1,"finish\r\n");
    
    my_printf(&huart3, "page FM\xff\xff\xff");
    HMI_Send_Float(huart3,"x0",fm_freq,2);
    HMI_Send_Float(huart3,"x1",fm_freq*mf_value,2);
    HMI_Send_Float(huart3,"x2",mf_value,2);
}

void Analysis_ASK(void)
{
    float max_harmonic = 0;   // ���г������
    int max_harmonic_idx = 0; // ���г����������

    for (int i = 10; i < FFT_LENGTH / 2; i++) // ֻ�迼��ǰһ��Ƶ�ף�NyquistƵ�ʣ�
    {
        if (FFT_OutputBuf[i] > max_harmonic)
        {
            max_harmonic = FFT_OutputBuf[i];
            max_harmonic_idx = i;
        }
    }

    // ����FFTƵ�ʣ�Hz��
    // Ƶ�ʼ��㹫ʽ: freq = idx * sampling_freq / FFT_LENGTH
    float fft_frequency = (float)max_harmonic_idx * sampling_frequency / (float)FFT_LENGTH;
   // my_printf(&huart1,"max_harmonic_idx=%d\r\nmax_freq=%.2f\r\n%.2f\r\n",max_harmonic_idx,fft_frequency,FFT_OutputBuf[max_harmonic_idx]);
    
    float right_freq,right_amp,left_freq, left_amp;
    for(int i=max_harmonic_idx+1;i<BUFFER_SIZE/2;i++)
    {
        
        if(FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1]&&FFT_OutputBuf[i]>10)//���10��Ҫ�Լ����ƣ���Ȼ���ܼ�⵽һЩ0.����
        {
             right_freq = i * sampling_frequency / (float)FFT_LENGTH;
            //my_printf(&huart1,"right_harmonic_idx=%d\r\nright_freq=%.2\r\nf%.2f\r\n",i,right_freq,FFT_OutputBuf[i]);
            break;
        }
    }
    for(int i=max_harmonic_idx-1;i>100;i--)
    {
        
        if(FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1]&&FFT_OutputBuf[i]>10)//���10��Ҫ�Լ����ƣ���Ȼ���ܼ�⵽һЩ0.����
        {
             left_freq = i * sampling_frequency / (float)FFT_LENGTH;
            //my_printf(&huart1,"left_harmonic_idx=%d\r\nleft_freq=%.2f\r\n%.2f\r\n",i,left_freq,FFT_OutputBuf[i]);
            break;
        }
    }
    float ask_freq=(right_freq-left_freq);
    my_printf(&huart1,"ASK:\r\n");
    my_printf(&huart1,"freq=%.2f\r\n",ask_freq);
    
    my_printf(&huart3, "page ASK\xff\xff\xff");
    HMI_Send_Float(huart3,"x0",ask_freq,2);
}

void Analysis_FSK(void)
{
    float max_harmonic = 0;   // ���г������
    int max_harmonic_idx = 0; // ���г����������

    for (int i = 10; i < FFT_LENGTH / 2; i++) // ֻ�迼��ǰһ��Ƶ�ף�NyquistƵ�ʣ�
    {
        if (FFT_OutputBuf[i] > max_harmonic)
        {
            max_harmonic = FFT_OutputBuf[i];
            max_harmonic_idx = i;
        }
    }

    // ����FFTƵ�ʣ�Hz��
    // Ƶ�ʼ��㹫ʽ: freq = idx * sampling_freq / FFT_LENGTH
    float fft_frequency = (float)max_harmonic_idx * sampling_frequency / (float)FFT_LENGTH;
    FM_Spectrum up_info,down_info;
    for(int i=max_harmonic_idx-1;i>10;i--)
    {
        if(FFT_OutputBuf[i]>10.0f&&FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1])
        {
            up_info.idx=i;
            up_info.harmonic=FFT_OutputBuf[i];
            up_info.freq=(float)up_info.idx* sampling_frequency / (float)FFT_LENGTH;
            break;
        }
    }
    
    for(int i=max_harmonic_idx+1;i<FFT_LENGTH/2;i++)
    {
        if(FFT_OutputBuf[i]>20.0f&&FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1])
        {
            down_info.idx=i;
            down_info.harmonic=FFT_OutputBuf[i];
            down_info.freq=(float)down_info.idx* sampling_frequency / (float)FFT_LENGTH;
            break;
        }
    }
    //my_printf(&huart1,"fsk=%.2f %.2f\r\n",down_info.freq,up_info.freq);
    float fsk_freq=down_info.freq-up_info.freq;
    
    FM_Spectrum temp[10];
    temp[0].harmonic=0.0f;
    uint8_t temp_index=0;
    for(int i=817;i<FFT_LENGTH/2;i++)//1188
    {
        if(FFT_OutputBuf[i]>30.0f&&FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1])
        {
            temp[temp_index].idx=i;
            temp[temp_index].harmonic=FFT_OutputBuf[i];
            temp[temp_index].freq=(float)temp[temp_index].idx* sampling_frequency / (float)FFT_LENGTH;
            my_printf(&huart1,"temp=%d %.2f %.2f\r\n",temp[temp_index].idx,temp[temp_index].harmonic,temp[temp_index].freq);
            if(temp_index>1&&fabsf(temp[temp_index].harmonic-temp[0].harmonic)<35.0f)
            {
                break;
            }
            temp_index++;
        }
    }
     my_printf(&huart1,"diff=%.2f\r\n",((temp[temp_index].freq+temp[0].freq)/2.0f));
    my_printf(&huart1,"chu=%.2f\r\n",(fsk_freq/4.0f));
    float h_value=((temp[temp_index].freq+temp[0].freq)/2.0f-50000.0f)/(fsk_freq/2.0f);
    my_printf(&huart1,"temp_index=%d\r\n",temp_index);
    my_printf(&huart1,"FSK:\r\n");
    my_printf(&huart1,"h=%.2f freq=%.2f\r\n",h_value,fsk_freq);
    
    my_printf(&huart3, "page FSK\xff\xff\xff");
    HMI_Send_Float(huart3,"x0",fsk_freq,2);
    HMI_Send_Float(huart3,"x1",h_value,2);
}
void Analysis_PSK(void)
{
    FM_Spectrum temp[2];
    uint8_t index=0;
    for(int i=10;i<1000;i++)
    {
        if (FFT_OutputBuf[i] > 250.0f&&FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1])
        {
            temp[index].idx=i;
            temp[index].harmonic=FFT_OutputBuf[i];
            temp[index].freq=(float)temp[index].idx* sampling_frequency / (float)FFT_LENGTH;
            index++;
        }
    }
    my_printf(&huart1,"PSK:\r\n");
    my_printf(&huart1,"%.2f\r\n",fabsf(temp[0].freq-temp[1].freq));
    
    my_printf(&huart3, "page PSK\xff\xff\xff");
    HMI_Send_Float(huart3,"x0",fabsf(temp[0].freq-temp[1].freq),2);
}
FM_Spectrum wave_info[50];
uint8_t wave_type;
uint8_t wave_judge(void)
{
    /*for(int i=10;i<FFT_LENGTH/2;i++)
    {
        if(FFT_OutputBuf[i]>5.0f&&FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1])
        {
            my_printf(&huart1,"up idx=%d amp=%.2f freq=%.2f\r\n",i,FFT_OutputBuf[i],
        (float)i* sampling_frequency / (float)FFT_LENGTH);
        }
    }
    */
    FM_Spectrum up_info[2],down_info[2];
    uint8_t up_index=0,down_index=0;
    for(int i=817;i>500;i--)
    {
        if(FFT_OutputBuf[i]>70.0f&&FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1])
        {
            up_info[up_index].idx=i;
            up_info[up_index].harmonic=FFT_OutputBuf[i];
            up_info[up_index].freq=(float)up_info[up_index].idx* sampling_frequency / (float)FFT_LENGTH;
            up_index++;
            if(up_index>=2)break;
        }
    }
    
    for(int i=821;i<1000;i++)
    {
        if(FFT_OutputBuf[i]>70.0f&&FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1])
        {
            down_info[down_index].idx=i;
            down_info[down_index].harmonic=FFT_OutputBuf[i];
            down_info[down_index].freq=(float)down_info[down_index].idx* sampling_frequency / (float)FFT_LENGTH;
            down_index++;
            if(down_index>=2)break;
        }
    }
    
    
    /*my_printf(&huart1,"up idx=%d amp=%.2f freq=%.2f\r\n",up_info.idx,up_info.harmonic
        ,up_info.freq);
    my_printf(&huart1,"down idx=%d amp=%.2f freq=%.2f\r\n",down_info.idx,down_info.harmonic
        ,down_info.freq);*/ 
    uint8_t exceed320_count=0;
    uint8_t exceed15_count=0;
    uint8_t exceed40_count=0;
    for(int i=10;i<FFT_LENGTH/2;i++)
    {
        if(FFT_OutputBuf[i]>320&&FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1])
        {
            exceed320_count++;
        }
    }
    if(exceed320_count==1)//CW
    {
        for(int i=10;i<FFT_LENGTH/2;i++)
        {
            if(FFT_OutputBuf[i]>15&&FFT_OutputBuf[i]>FFT_OutputBuf[i+1]&&FFT_OutputBuf[i]>FFT_OutputBuf[i-1])
            {
                exceed15_count++;
            }
        }
        if(exceed15_count==1)
        {
            wave_type=0;
            my_printf(&huart1,"CW\r\n");
             my_printf(&huart3, "page CW\xff\xff\xff");
            return 1;
        }
       else if(exceed15_count==3)
       {
            wave_type=1;
            Analysis_AM();
            return 1 ;
       }
       else
       {
           wave_type=3;
           Analysis_ASK();
           return 1 ;
       }
    }
    else if(exceed320_count==2)
    {
         Analysis_PSK();
        wave_type=5;
        return 1 ;
        
    }
    else if(exceed320_count>2)
    {
        /*my_printf(&huart1,"up=%d %.2f down=%d %.2f\r\n",up_info[0].idx,up_info[0].harmonic,down_info[0].idx,down_info[0].harmonic);
        my_printf(&huart1,"up=%d %.2f down=%d %.2f\r\n",up_info[1].idx,up_info[1].harmonic,down_info[1].idx,down_info[1].harmonic);*/
        if(FFT_OutputBuf[819]>20.0f)
        {
            for(int i=10;i<819;i++)
            {
                if(FFT_OutputBuf[i]>FFT_OutputBuf[819])
                {
                    wave_type=2;
                    Analysis_FM();
                    return 1;
                }
            }
            if(FFT_OutputBuf[819]>600.0f)
            {
                wave_type=2;
                Analysis_FM();
                return 1;
            }
            else
            {
                wave_type=4;
                Analysis_FSK();
                return 1;
            }
        }
        else
        {
            for(int i=10;i<up_info[0].idx;i++)
            {
                if(FFT_OutputBuf[i]>up_info[0].harmonic)
                {
                    wave_type=2;
                    Analysis_FM();
                    return 1;
                }
            }
            if(up_info[0].harmonic>600.0f)
            {
                wave_type=2;
                Analysis_FM();
                return 1;
            }
            else
            {
                wave_type=4;
                Analysis_FSK();
                return 1;
            }
        }
    }
   // my_printf(&huart1,"exceed25_count=%d\r\n",exceed15_count);
    return 0;
}

uint32_t ms200_tick=0;
uint8_t ms200_flag=0;
void adc_task(void)
{
    if (AdcConvEnd) 
    {
       /* for (uint16_t i = 0; i < BUFFER_SIZE; i++)
        {
            my_printf(&huart1, "%d\r\n", (int)adc_val_buffer[i]);
        }
        */
        calculate_fft(&scfft,adc_val_buffer,FFT_OutputBuf,FFT_LENGTH);
        concentrate_energy(FFT_OutputBuf,FFT_LENGTH,14);
       /* for (int i = 0; i < FFT_LENGTH; i++)
        {
            my_printf(&huart1,"%.2f\r\n",FFT_OutputBuf[i]);
            
        }*/
        if(wave_judge()==0)
        {
            my_printf(&huart1,"Not Found\r\n");
             my_printf(&huart3, "page None\xff\xff\xff");
        }
        //my_printf(&huart1,"type=%d\r\n",wave_type);
    
        
       /* switch(wave)
        {
            case 0:
                
            break;
            
            case 1:
                Analysis_AM();
            break;
            
            case 2:
                Analysis_FM();
            break;
        }*/
         /*   
        wave_data.vpp=Get_Waveform_Vpp(dac_val_buffer, & wave_data.mean, & wave_data.rms);  
        my_printf(&huart1, "Vpp: %.2f V, average: %.2f V, valid: %.2f V\r\n", wave_data.vpp, wave_data.mean, wave_data.rms);        
        // ��������˲��η�����־������в�
        {�η���*/
      /*  if (wave_analysis_flag)
            wave_data = Get_Waveform_Info(dac_val_buffer);

            // ���ݲ�ѯ���ʹ�ӡ��Ӧ����Ϣ
            switch (wave_query_type)
            {
            case 4: // ��ӡȫ����Ϣ
                my_printf(&huart1, "%d:%.2f %s\r\n",dac_app_get_update_frequency() / WAVEFORM_SAMPLES,
                          (uint32_t)wave_data.frequency,
                          wave_data.vpp,
                          GetWaveformTypeString(wave_data.waveform_type));
                break;

            case 1: // ����ӡ��������
                my_printf(&huart1, "%s\r\n", GetWaveformTypeString(wave_data.waveform_type));
                break;

            case 2: // ����ӡƵ��
                my_printf(&huart1, "%dHz\r\n", (uint32_t)wave_data.frequency);
                break;

            case 3: // ����ӡ���ֵ
                my_printf(&huart1, "%.2fmV\r\n", wave_data.vpp);
                break;
            }

            wave_analysis_flag = 0; // ������ɺ������־
            wave_query_type = 0;    // ���ò�ѯ����
        }
*/
        // --- ������� ---

        // ��մ��������� (��ѡ��ȡ���ں����߼�)
        // memset(dac_val_buffer, 0, sizeof(uint32_t) * (BUFFER_SIZE / 2));

        // ��� ADC DMA �������ͱ�־λ��׼����һ�βɼ�
        // memset(adc_val_buffer, 0, sizeof(uint32_t) * BUFFER_SIZE); // ���ԭʼ���� (�����Ҫ)
        AdcConvEnd = 0;

        // �������� ADC DMA ������һ�βɼ�
        // ע�⣺�����ʱ������������ ADC �ģ����ܲ���Ҫ�ֶ�ֹͣ/���� DMA
        // ��Ҫ���� TIM3 �� ADC �ľ������þ����Ƿ���Ҫ��������
      /* HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
        __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT); // �ٴν��ð봫���ж�*/
       ms200_flag=1; 
    }
    if(ms200_flag==1)
    {
        if(++ms200_tick==500)
        {
            HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
            __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);
            ms200_tick=0;
            ms200_flag=0;
        }
    }
}


